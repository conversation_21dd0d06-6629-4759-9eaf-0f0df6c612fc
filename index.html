<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solar System Explorer</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/PointerLockControls.js"></script>
</head>
<body>
    <div id="container"></div>
    <div id="ui">
        <div id="mode-toggle">
            <button id="mode-button">Switch to Artistic Mode</button>
        </div>
        <div id="speed-control">
            <label for="speed-slider">Travel Speed: <span id="speed-value">1x</span></label>
            <input type="range" id="speed-slider" min="0.1" max="10" step="0.1" value="1">
        </div>
        <div id="planet-buttons">
            <button data-planet="sun">Sun</button>
            <button data-planet="mercury">Mercury</button>
            <button data-planet="venus">Venus</button>
            <button data-planet="earth">Earth</button>
            <button data-planet="mars">Mars</button>
            <button data-planet="jupiter">Jupiter</button>
            <button data-planet="saturn">Saturn</button>
            <button data-planet="uranus">Uranus</button>
            <button data-planet="neptune">Neptune</button>
        </div>
        <div id="controls-hint">
            Controls: WASD - Move | QE - Up/Down | Mouse - Look
        </div>
    </div>
    <script src="app.js"></script>
</body>
</html> 