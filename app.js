// Scene setup
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 100000);
const renderer = new THREE.WebGLRenderer();
renderer.setSize(window.innerWidth, window.innerHeight);
document.getElementById('container').appendChild(renderer.domElement);

// Controls (6DoF with PointerLock for mouse look)
const controls = new THREE.PointerLockControls(camera, document.body);
scene.add(controls.getObject());
document.addEventListener('click', () => controls.lock());

// Movement variables
const moveSpeed = 10;
let velocity = new THREE.Vector3();
let direction = new THREE.Vector3();
let moveForward = false, moveBackward = false, moveLeft = false, moveRight = false, moveUp = false, moveDown = false;

// Keyboard controls
document.addEventListener('keydown', (e) => {
    switch (e.key.toLowerCase()) {
        case 'w': moveForward = true; break;
        case 's': moveBackward = true; break;
        case 'a': moveLeft = true; break;
        case 'd': moveRight = true; break;
        case 'q': moveUp = true; break;
        case 'e': moveDown = true; break;
    }
});
document.addEventListener('keyup', (e) => {
    switch (e.key.toLowerCase()) {
        case 'w': moveForward = false; break;
        case 's': moveBackward = false; break;
        case 'a': moveLeft = false; break;
        case 'd': moveRight = false; break;
        case 'q': moveUp = false; break;
        case 'e': moveDown = false; break;
    }
});

// Solar system data (positions scaled down for navigation)
const planets = {
    sun: { pos: new THREE.Vector3(0, 0, 0), radius: 5, color: 0xffff00, texture: null },
    mercury: { pos: new THREE.Vector3(10, 0, 0), radius: 0.5, color: 0xaaaaaa, texture: null },
    venus: { pos: new THREE.Vector3(20, 0, 0), radius: 1, color: 0xffcc00, texture: null },
    earth: { pos: new THREE.Vector3(30, 0, 0), radius: 1, color: 0x0000ff, texture: null },
    mars: { pos: new THREE.Vector3(40, 0, 0), radius: 0.8, color: 0xff0000, texture: null },
    jupiter: { pos: new THREE.Vector3(60, 0, 0), radius: 3, color: 0xffa500, texture: null },
    saturn: { pos: new THREE.Vector3(80, 0, 0), radius: 2.5, color: 0xffff99, texture: null },
    uranus: { pos: new THREE.Vector3(100, 0, 0), radius: 2, color: 0x00ffff, texture: null },
    neptune: { pos: new THREE.Vector3(120, 0, 0), radius: 2, color: 0x0000cc, texture: null },
};

let planetMeshes = {};
let isRealistic = true;

// Create planets
Object.keys(planets).forEach(key => {
    const geometry = new THREE.SphereGeometry(planets[key].radius, 32, 32);
    const material = new THREE.MeshBasicMaterial({ color: planets[key].color });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.copy(planets[key].pos);
    scene.add(mesh);
    planetMeshes[key] = mesh;
});

// Lighting (for realistic mode)
const sunLight = new THREE.PointLight(0xffffff, 2, 1000);
sunLight.position.set(0, 0, 0);
scene.add(sunLight);
const ambientLight = new THREE.AmbientLight(0x404040);
scene.add(ambientLight);

// Mode toggle
const modeButton = document.getElementById('mode-button');
modeButton.addEventListener('click', () => {
    isRealistic = !isRealistic;
    modeButton.textContent = isRealistic ? 'Switch to Artistic Mode' : 'Switch to Realistic Mode';
    updateModes();
});

function updateModes() {
    Object.values(planetMeshes).forEach(mesh => {
        if (isRealistic) {
            mesh.material.wireframe = false;
            mesh.material.color.setHex(mesh.userData.originalColor);
        } else {
            mesh.material.wireframe = true;
            mesh.material.color.setHex(0xffffff * Math.random());
        }
    });
    sunLight.visible = isRealistic;
    ambientLight.intensity = isRealistic ? 0.2 : 1;
}

// Store original colors
Object.keys(planetMeshes).forEach(key => {
    planetMeshes[key].userData.originalColor = planets[key].color;
});
updateModes(); // Initial setup

// Planet buttons
document.querySelectorAll('#planet-buttons button').forEach(button => {
    button.addEventListener('click', () => {
        const planet = button.dataset.planet;
        travelToPlanet(planet);
    });
});

// Speed slider
const speedSlider = document.getElementById('speed-slider');
const speedValue = document.getElementById('speed-value');
speedSlider.addEventListener('input', () => {
    speedValue.textContent = `${speedSlider.value}x`;
});

// Travel function with animation
let targetPosition = null;
let travelSpeed = 1;

function travelToPlanet(planet) {
    targetPosition = planets[planet].pos.clone();
    targetPosition.add(new THREE.Vector3(0, 0, planets[planet].radius * 2)); // Offset for viewing
    travelSpeed = parseFloat(speedSlider.value);
}

// Animation loop
function animate() {
    requestAnimationFrame(animate);

    // Movement
    velocity.set(0, 0, 0);
    direction.z = Number(moveForward) - Number(moveBackward);
    direction.x = Number(moveRight) - Number(moveLeft);
    direction.y = Number(moveUp) - Number(moveDown);
    direction.normalize();

    if (moveForward || moveBackward) velocity.z = direction.z * moveSpeed;
    if (moveLeft || moveRight) velocity.x = direction.x * moveSpeed;
    if (moveUp || moveDown) velocity.y = direction.y * moveSpeed;

    controls.moveRight(velocity.x);
    controls.moveForward(velocity.z);
    controls.getObject().position.y += velocity.y;

    // Travel animation
    if (targetPosition) {
        const dir = targetPosition.clone().sub(camera.position).normalize();
        camera.position.add(dir.multiplyScalar(moveSpeed * travelSpeed));
        if (camera.position.distanceTo(targetPosition) < 1) targetPosition = null;
    }

    renderer.render(scene, camera);
}
animate();

// Resize handler
window.addEventListener('resize', () => {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}); 