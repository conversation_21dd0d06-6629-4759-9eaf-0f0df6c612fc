{"name": "solar-system-explorer", "version": "1.0.0", "description": "A 3D interactive solar system explorer with 6DoF controls and realistic/artistic modes", "type": "module", "main": "src/main.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .js", "format": "prettier --write src/**/*.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["solar-system", "3d", "threejs", "webgl", "space", "astronomy"], "author": "", "license": "MIT", "devDependencies": {"@eslint/js": "^9.31.0", "@vitejs/plugin-legacy": "^7.0.1", "eslint": "^9.31.0", "prettier": "^3.6.2", "vite": "^7.0.5"}, "dependencies": {"stats.js": "^0.17.0", "three": "^0.178.0"}}