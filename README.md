"# Solar System Explorer

A simple HTML5-based 3D explorer for the solar system with 6DoF controls, planet navigation buttons, variable travel speeds, and switchable realistic/artistic modes.

## Features

- **3D Solar System Model**: Sun, 8 planets, and major moons with accurate or stylized visuals.
- **6DoF Controls**: Free movement and rotation using keyboard (WASDQE) and mouse.
- **Planet Navigation**: Buttons to travel to any planet at adjustable speeds (via slider).
- **Modes**: Toggle between realistic (accurate scales, textures) and artistic (enhanced visuals).
- **Responsive UI**: Works on desktop and mobile browsers with WebGL support.

## Setup

1. Open `index.html` in a modern web browser (Chrome, Firefox, etc.).
2. No installation required—uses Three.js via CDN.
3. For development: Edit `app.js` for logic, `style.css` for styling.

## Usage

- **Controls**:
  - WASD: Move forward/left/back/right.
  - QE: Move up/down.
  - Mouse: Look around (click and drag).
  - Slider: Adjust travel speed.
  - Buttons: Click a planet to travel to it.
  - Toggle: Switch between realistic/artistic modes.
- Explore freely or use buttons for quick navigation.

## Dependencies

- Three.js (loaded via CDN in index.html).

For questions or improvements, feel free to modify the code!"
