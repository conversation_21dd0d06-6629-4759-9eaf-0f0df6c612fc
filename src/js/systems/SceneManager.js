/**
 * SceneManager - Manages the Three.js scene, camera, renderer, and lighting
 */

import * as THREE from 'three';

export class SceneManager {
  constructor(container) {
    this.container = container;

    // Core Three.js components
    this.scene = null;
    this.camera = null;
    this.renderer = null;

    // Lighting
    this.ambientLight = null;
    this.sunLight = null;

    // Scene objects
    this.celestialBodies = new Map();
    this.starField = null;

    // Settings
    this.currentMode = 'realistic'; // 'realistic' or 'artistic'
    this.timeScale = 1.0;

    // Performance
    this.renderTarget = null;
  }

  /**
   * Initialize the scene manager
   */
  async init() {
    this.createScene();
    this.createCamera();
    this.createRenderer();
    this.createLighting();
    await this.createStarField();

    console.log('✅ SceneManager initialized');
  }

  /**
   * Create the Three.js scene
   */
  createScene() {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x000000);

    // Add fog for depth perception (disabled by default)
    // this.scene.fog = new THREE.Fog(0x000000, 1000, 10000);
  }

  /**
   * Create the camera
   */
  createCamera() {
    const aspect = this.container.clientWidth / this.container.clientHeight;

    this.camera = new THREE.PerspectiveCamera(
      60, // Field of view
      aspect, // Aspect ratio
      0.1, // Near clipping plane
      100000 // Far clipping plane
    );

    // Set initial camera position
    this.camera.position.set(0, 0, 50);
    this.camera.lookAt(0, 0, 0);
  }

  /**
   * Create the WebGL renderer
   */
  createRenderer() {
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: false,
      powerPreference: 'high-performance',
    });

    // Configure renderer
    this.renderer.setSize(
      this.container.clientWidth,
      this.container.clientHeight
    );
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

    // Enable shadows
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // Set color space
    this.renderer.outputColorSpace = THREE.SRGBColorSpace;

    // Enable tone mapping for better lighting
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
    this.renderer.toneMappingExposure = 1.0;

    // Append to container
    this.container.appendChild(this.renderer.domElement);
  }

  /**
   * Create lighting system
   */
  createLighting() {
    // Ambient light for general illumination
    this.ambientLight = new THREE.AmbientLight(0x404040, 0.2);
    this.scene.add(this.ambientLight);

    // Sun light (point light at origin)
    this.sunLight = new THREE.PointLight(0xffffff, 2, 0);
    this.sunLight.position.set(0, 0, 0);
    this.sunLight.castShadow = true;

    // Configure shadow properties
    this.sunLight.shadow.mapSize.width = 2048;
    this.sunLight.shadow.mapSize.height = 2048;
    this.sunLight.shadow.camera.near = 0.1;
    this.sunLight.shadow.camera.far = 1000;

    this.scene.add(this.sunLight);
  }

  /**
   * Create a starfield background
   */
  async createStarField() {
    const starCount = 10000;
    const starGeometry = new THREE.BufferGeometry();
    const starPositions = new Float32Array(starCount * 3);
    const starColors = new Float32Array(starCount * 3);

    // Generate random star positions and colors
    for (let i = 0; i < starCount; i++) {
      const i3 = i * 3;

      // Random position on a sphere
      const radius = 50000;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.acos(Math.random() * 2 - 1);

      starPositions[i3] = radius * Math.sin(phi) * Math.cos(theta);
      starPositions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      starPositions[i3 + 2] = radius * Math.cos(phi);

      // Random star color (bluish to reddish)
      const temperature = Math.random();
      starColors[i3] = 0.5 + temperature * 0.5; // Red
      starColors[i3 + 1] = 0.5 + temperature * 0.3; // Green
      starColors[i3 + 2] = 0.8 + temperature * 0.2; // Blue
    }

    starGeometry.setAttribute(
      'position',
      new THREE.BufferAttribute(starPositions, 3)
    );
    starGeometry.setAttribute(
      'color',
      new THREE.BufferAttribute(starColors, 3)
    );

    const starMaterial = new THREE.PointsMaterial({
      size: 2,
      vertexColors: true,
      transparent: true,
      opacity: 0.8,
    });

    this.starField = new THREE.Points(starGeometry, starMaterial);
    this.scene.add(this.starField);
  }

  /**
   * Load solar system data and create celestial bodies
   */
  async loadSolarSystem() {
    // This will be implemented when we create the CelestialBody class
    console.log('🌌 Loading solar system...');

    // For now, create a simple sun as placeholder
    const sunGeometry = new THREE.SphereGeometry(5, 32, 32);
    const sunMaterial = new THREE.MeshBasicMaterial({
      color: 0xffff00,
      emissive: 0xffaa00,
      emissiveIntensity: 0.3,
    });
    const sun = new THREE.Mesh(sunGeometry, sunMaterial);
    sun.position.set(0, 0, 0);
    this.scene.add(sun);

    this.celestialBodies.set('sun', sun);
  }

  /**
   * Update the scene
   */
  update(deltaTime) {
    // Update celestial body animations
    this.celestialBodies.forEach((body, name) => {
      if (name === 'sun') {
        // Rotate the sun
        body.rotation.y += deltaTime * 0.1;
      }
    });

    // Subtle starfield rotation
    if (this.starField) {
      this.starField.rotation.y += deltaTime * 0.001;
    }
  }

  /**
   * Render the scene
   */
  render() {
    this.renderer.render(this.scene, this.camera);
  }

  /**
   * Handle window resize
   */
  onWindowResize() {
    const width = this.container.clientWidth;
    const height = this.container.clientHeight;

    // Update camera
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();

    // Update renderer
    this.renderer.setSize(width, height);
  }

  /**
   * Switch between realistic and artistic modes
   */
  setVisualizationMode(mode) {
    if (mode === this.currentMode) return;

    this.currentMode = mode;

    if (mode === 'realistic') {
      this.ambientLight.intensity = 0.2;
      this.sunLight.intensity = 2;
      this.renderer.toneMappingExposure = 1.0;
    } else {
      this.ambientLight.intensity = 0.5;
      this.sunLight.intensity = 1.5;
      this.renderer.toneMappingExposure = 1.2;
    }

    console.log(`🎨 Switched to ${mode} mode`);
  }

  /**
   * Get celestial body by name
   */
  getCelestialBody(name) {
    return this.celestialBodies.get(name);
  }

  /**
   * Get all celestial bodies
   */
  getAllCelestialBodies() {
    return Array.from(this.celestialBodies.values());
  }

  /**
   * Dispose of resources
   */
  dispose() {
    // Dispose of geometries and materials
    this.celestialBodies.forEach(body => {
      if (body.geometry) body.geometry.dispose();
      if (body.material) {
        if (Array.isArray(body.material)) {
          body.material.forEach(material => material.dispose());
        } else {
          body.material.dispose();
        }
      }
    });

    // Dispose of starfield
    if (this.starField) {
      this.starField.geometry.dispose();
      this.starField.material.dispose();
    }

    // Dispose of renderer
    if (this.renderer) {
      this.renderer.dispose();
      if (this.renderer.domElement.parentNode) {
        this.renderer.domElement.parentNode.removeChild(
          this.renderer.domElement
        );
      }
    }

    console.log('🧹 SceneManager disposed');
  }
}
