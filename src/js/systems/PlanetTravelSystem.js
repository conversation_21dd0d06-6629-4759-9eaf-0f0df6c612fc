/**
 * PlanetTravelSystem - Handles smooth camera transitions between celestial bodies
 */

import * as THREE from 'three';
import { CAMERA_POSITIONS } from '../data/SolarSystemData.js';

export class PlanetTravelSystem {
  constructor(camera, sceneManager, navigationControls) {
    this.camera = camera;
    this.sceneManager = sceneManager;
    this.navigationControls = navigationControls;
    
    // Travel state
    this.isTransitioning = false;
    this.currentTarget = null;
    this.startPosition = new THREE.Vector3();
    this.targetPosition = new THREE.Vector3();
    this.startLookAt = new THREE.Vector3();
    this.targetLookAt = new THREE.Vector3();
    
    // Travel settings
    this.travelSpeed = 1.0; // Multiplier for travel speed
    this.transitionDuration = 3.0; // Base duration in seconds
    this.currentTransitionTime = 0;
    
    // Easing function
    this.easingFunction = this.easeInOutCubic;
  }

  /**
   * Travel to a specific celestial body
   */
  async travelTo(bodyName, speed = 1.0) {
    if (this.isTransitioning) {
      console.warn('Already transitioning to another target');
      return false;
    }

    const targetBody = this.sceneManager.getCelestialBody(bodyName);
    if (!targetBody) {
      console.error(`Celestial body '${bodyName}' not found`);
      return false;
    }

    console.log(`🚀 Starting travel to ${bodyName}`);

    // Set up transition
    this.isTransitioning = true;
    this.currentTarget = bodyName;
    this.travelSpeed = speed;
    this.currentTransitionTime = 0;

    // Store starting position and look direction
    this.startPosition.copy(this.camera.position);
    this.calculateLookDirection(this.startLookAt);

    // Calculate target position and look direction
    this.calculateTargetPosition(targetBody, bodyName);

    return new Promise((resolve) => {
      this.transitionResolve = resolve;
    });
  }

  /**
   * Calculate target camera position for a celestial body
   */
  calculateTargetPosition(targetBody, bodyName) {
    const bodyPosition = targetBody.getPosition();
    const cameraConfig = CAMERA_POSITIONS[bodyName] || { distance: 10, height: 3 };
    
    // Position camera at a good viewing distance
    const distance = cameraConfig.distance;
    const height = cameraConfig.height;
    
    // Calculate position offset from the body
    const offset = new THREE.Vector3(distance, height, distance * 0.5);
    this.targetPosition.copy(bodyPosition).add(offset);
    
    // Look at the celestial body
    this.targetLookAt.copy(bodyPosition);
  }

  /**
   * Calculate current look direction
   */
  calculateLookDirection(lookAt) {
    const direction = new THREE.Vector3();
    this.camera.getWorldDirection(direction);
    lookAt.copy(this.camera.position).add(direction.multiplyScalar(100));
  }

  /**
   * Update the travel system
   */
  update(deltaTime) {
    if (!this.isTransitioning) return;

    // Update transition time
    this.currentTransitionTime += deltaTime * this.travelSpeed;
    const duration = this.transitionDuration / this.travelSpeed;
    const progress = Math.min(this.currentTransitionTime / duration, 1.0);

    // Apply easing
    const easedProgress = this.easingFunction(progress);

    // Interpolate position
    const currentPosition = new THREE.Vector3().lerpVectors(
      this.startPosition,
      this.targetPosition,
      easedProgress
    );

    // Interpolate look direction
    const currentLookAt = new THREE.Vector3().lerpVectors(
      this.startLookAt,
      this.targetLookAt,
      easedProgress
    );

    // Update camera
    this.camera.position.copy(currentPosition);
    this.camera.lookAt(currentLookAt);

    // Update navigation controls to match camera orientation
    if (this.navigationControls.euler) {
      const direction = currentLookAt.clone().sub(currentPosition).normalize();
      this.navigationControls.euler.y = Math.atan2(direction.x, direction.z);
      this.navigationControls.euler.x = Math.asin(-direction.y);
    }

    // Check if transition is complete
    if (progress >= 1.0) {
      this.completeTransition();
    }
  }

  /**
   * Complete the current transition
   */
  completeTransition() {
    this.isTransitioning = false;
    
    console.log(`✅ Arrived at ${this.currentTarget}`);
    
    if (this.transitionResolve) {
      this.transitionResolve(this.currentTarget);
      this.transitionResolve = null;
    }
    
    this.currentTarget = null;
  }

  /**
   * Cancel current transition
   */
  cancelTransition() {
    if (!this.isTransitioning) return;
    
    this.isTransitioning = false;
    console.log('🛑 Travel cancelled');
    
    if (this.transitionResolve) {
      this.transitionResolve(null);
      this.transitionResolve = null;
    }
    
    this.currentTarget = null;
  }

  /**
   * Set travel speed multiplier
   */
  setTravelSpeed(speed) {
    this.travelSpeed = Math.max(0.1, Math.min(10.0, speed));
  }

  /**
   * Get current travel speed
   */
  getTravelSpeed() {
    return this.travelSpeed;
  }

  /**
   * Check if currently transitioning
   */
  isTransitioning() {
    return this.isTransitioning;
  }

  /**
   * Get current target
   */
  getCurrentTarget() {
    return this.currentTarget;
  }

  /**
   * Get transition progress (0-1)
   */
  getTransitionProgress() {
    if (!this.isTransitioning) return 0;
    
    const duration = this.transitionDuration / this.travelSpeed;
    return Math.min(this.currentTransitionTime / duration, 1.0);
  }

  /**
   * Easing function: ease in-out cubic
   */
  easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  /**
   * Easing function: ease in-out sine
   */
  easeInOutSine(t) {
    return -(Math.cos(Math.PI * t) - 1) / 2;
  }

  /**
   * Easing function: ease out expo
   */
  easeOutExpo(t) {
    return t === 1 ? 1 : 1 - Math.pow(2, -10 * t);
  }

  /**
   * Set easing function
   */
  setEasingFunction(easingName) {
    const easingFunctions = {
      'cubic': this.easeInOutCubic,
      'sine': this.easeInOutSine,
      'expo': this.easeOutExpo
    };
    
    if (easingFunctions[easingName]) {
      this.easingFunction = easingFunctions[easingName];
    }
  }

  /**
   * Get distance to target body
   */
  getDistanceToTarget() {
    if (!this.currentTarget) return 0;
    
    const targetBody = this.sceneManager.getCelestialBody(this.currentTarget);
    if (!targetBody) return 0;
    
    return this.camera.position.distanceTo(targetBody.getPosition());
  }

  /**
   * Quick travel (instant teleport)
   */
  quickTravelTo(bodyName) {
    const targetBody = this.sceneManager.getCelestialBody(bodyName);
    if (!targetBody) {
      console.error(`Celestial body '${bodyName}' not found`);
      return false;
    }

    // Cancel any current transition
    this.cancelTransition();

    // Calculate target position
    this.calculateTargetPosition(targetBody, bodyName);

    // Instantly move camera
    this.camera.position.copy(this.targetPosition);
    this.camera.lookAt(this.targetLookAt);

    // Update navigation controls
    if (this.navigationControls.euler) {
      const direction = this.targetLookAt.clone().sub(this.targetPosition).normalize();
      this.navigationControls.euler.y = Math.atan2(direction.x, direction.z);
      this.navigationControls.euler.x = Math.asin(-direction.y);
    }

    console.log(`⚡ Quick travel to ${bodyName}`);
    return true;
  }
}
