/**
 * UIManager - Manages user interface interactions and updates
 */

export class UIManager {
  constructor(sceneManager, navigationControls) {
    this.sceneManager = sceneManager;
    this.navigationControls = navigationControls;

    // UI elements
    this.modeToggle = null;
    this.speedSlider = null;
    this.speedValue = null;
    this.planetButtons = null;
    this.helpToggle = null;
    this.helpPanel = null;
    this.infoPanel = null;
    this.currentTarget = null;
    this.targetInfo = null;

    // State
    this.currentMode = 'realistic';
    this.isHelpVisible = false;
    this.selectedPlanet = null;

    // Bind methods
    this.onModeToggle = this.onModeToggle.bind(this);
    this.onSpeedChange = this.onSpeedChange.bind(this);
    this.onPlanetSelect = this.onPlanetSelect.bind(this);
    this.onHelpToggle = this.onHelpToggle.bind(this);
  }

  /**
   * Initialize the UI manager
   */
  init() {
    this.setupElements();
    this.setupEventListeners();
    this.updateUI();

    console.log('✅ UIManager initialized');
  }

  /**
   * Set up UI element references
   */
  setupElements() {
    // Mode toggle
    this.modeToggle = document.getElementById('mode-toggle');

    // Speed controls
    this.speedSlider = document.getElementById('speed-slider');
    this.speedValue = document.getElementById('speed-value');

    // Planet buttons
    this.planetButtons = document.querySelectorAll('.planet-button');

    // Help panel
    this.helpToggle = document.getElementById('help-toggle');
    this.helpPanel = document.getElementById('help-panel');

    // Info panel
    this.infoPanel = document.getElementById('info-panel');
    this.currentTarget = document.getElementById('current-target');
    this.targetInfo = document.getElementById('target-info');

    // Check if all elements are found
    const elements = [
      this.modeToggle,
      this.speedSlider,
      this.speedValue,
      this.helpToggle,
      this.helpPanel,
      this.infoPanel,
      this.currentTarget,
      this.targetInfo,
    ];

    const missingElements = elements.filter(el => !el);
    if (missingElements.length > 0) {
      console.warn('Some UI elements not found:', missingElements);
    }
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Mode toggle
    if (this.modeToggle) {
      this.modeToggle.addEventListener('click', this.onModeToggle);
    }

    // Speed slider
    if (this.speedSlider) {
      this.speedSlider.addEventListener('input', this.onSpeedChange);
    }

    // Planet buttons
    this.planetButtons.forEach(button => {
      button.addEventListener('click', this.onPlanetSelect);
    });

    // Help toggle
    if (this.helpToggle) {
      this.helpToggle.addEventListener('click', this.onHelpToggle);
    }
  }

  /**
   * Handle mode toggle
   */
  onModeToggle() {
    this.currentMode =
      this.currentMode === 'realistic' ? 'artistic' : 'realistic';

    // Update scene manager
    this.sceneManager.setVisualizationMode(this.currentMode);

    // Update UI
    this.updateModeToggle();

    console.log(`🎨 Mode switched to: ${this.currentMode}`);
  }

  /**
   * Handle speed change
   */
  onSpeedChange() {
    if (!this.speedSlider || !this.speedValue) return;

    const speed = parseFloat(this.speedSlider.value);

    // Update navigation controls
    this.navigationControls.setMoveSpeed(speed * 10); // Scale for better feel

    // Update display
    this.speedValue.textContent = `${speed.toFixed(1)}x`;

    console.log(`🚀 Speed changed to: ${speed}x`);
  }

  /**
   * Handle planet selection
   */
  async onPlanetSelect(event) {
    const planetName = event.target.dataset.planet;
    if (!planetName) return;

    this.selectedPlanet = planetName;

    // Update info panel
    this.updateInfoPanel(planetName);

    // Travel to the selected planet
    const travelSystem = this.sceneManager.getPlanetTravelSystem();
    if (travelSystem) {
      const speed = this.speedSlider ? parseFloat(this.speedSlider.value) : 1.0;

      try {
        await this.sceneManager.travelTo(planetName, speed);
        this.showNotification(`Arrived at ${planetName}`, 'success');
      } catch (error) {
        console.error('Travel failed:', error);
        this.showNotification(`Failed to travel to ${planetName}`, 'error');
      }
    }

    console.log(`🪐 Traveling to: ${planetName}`);
  }

  /**
   * Handle help toggle
   */
  onHelpToggle() {
    this.isHelpVisible = !this.isHelpVisible;

    if (this.helpPanel) {
      if (this.isHelpVisible) {
        this.helpPanel.classList.remove('collapsed');
      } else {
        this.helpPanel.classList.add('collapsed');
      }
    }

    console.log(`❓ Help panel ${this.isHelpVisible ? 'shown' : 'hidden'}`);
  }

  /**
   * Update mode toggle appearance
   */
  updateModeToggle() {
    if (!this.modeToggle) return;

    const icon = this.modeToggle.querySelector('.mode-icon');
    const text = this.modeToggle.querySelector('.mode-text');

    if (this.currentMode === 'realistic') {
      this.modeToggle.className = 'mode-button realistic';
      if (icon) icon.textContent = '🔬';
      if (text) text.textContent = 'Realistic';
    } else {
      this.modeToggle.className = 'mode-button artistic';
      if (icon) icon.textContent = '🎨';
      if (text) text.textContent = 'Artistic';
    }
  }

  /**
   * Update info panel with planet information
   */
  updateInfoPanel(planetName) {
    if (!this.currentTarget || !this.targetInfo) return;

    // Planet data (simplified for now)
    const planetData = {
      sun: {
        name: 'The Sun',
        info: 'Our star - the center of the solar system. A massive ball of hot plasma held together by gravity.',
      },
      mercury: {
        name: 'Mercury',
        info: 'The smallest planet and closest to the Sun. Extreme temperature variations.',
      },
      venus: {
        name: 'Venus',
        info: 'The hottest planet with a thick, toxic atmosphere. Often called Earth\'s twin.',
      },
      earth: {
        name: 'Earth',
        info: 'Our home planet. The only known planet with life in the universe.',
      },
      mars: {
        name: 'Mars',
        info: 'The Red Planet. Has the largest volcano and canyon in the solar system.',
      },
      jupiter: {
        name: 'Jupiter',
        info: 'The largest planet. A gas giant with a Great Red Spot storm.',
      },
      saturn: {
        name: 'Saturn',
        info: 'Famous for its beautiful ring system. A gas giant less dense than water.',
      },
      uranus: {
        name: 'Uranus',
        info: 'An ice giant that rotates on its side. Has a faint ring system.',
      },
      neptune: {
        name: 'Neptune',
        info: 'The windiest planet with speeds up to 2,100 km/h. Deep blue color.',
      },
    };

    const data = planetData[planetName];
    if (data) {
      this.currentTarget.textContent = data.name;
      this.targetInfo.innerHTML = `<p>${data.info}</p>`;
    }
  }

  /**
   * Update UI elements
   */
  updateUI() {
    this.updateModeToggle();

    // Set initial speed value
    if (this.speedSlider && this.speedValue) {
      const speed = parseFloat(this.speedSlider.value);
      this.speedValue.textContent = `${speed.toFixed(1)}x`;
    }
  }

  /**
   * Update method called from main loop
   */
  update(_deltaTime) {
    // Update travel progress if traveling
    const travelSystem = this.sceneManager.getPlanetTravelSystem();
    if (travelSystem && travelSystem.isTransitioning) {
      const progress = travelSystem.getTransitionProgress();
      const target = travelSystem.getCurrentTarget();

      if (target && this.currentTarget) {
        this.currentTarget.textContent = `Traveling to ${target}... ${Math.round(progress * 100)}%`;
      }
    }
  }

  /**
   * Show notification message
   */
  showNotification(message, type = 'info') {
    // TODO: Implement notification system
    console.log(`📢 ${type.toUpperCase()}: ${message}`);
  }

  /**
   * Get current visualization mode
   */
  getCurrentMode() {
    return this.currentMode;
  }

  /**
   * Get selected planet
   */
  getSelectedPlanet() {
    return this.selectedPlanet;
  }

  /**
   * Dispose of the UI manager
   */
  dispose() {
    // Remove event listeners
    if (this.modeToggle) {
      this.modeToggle.removeEventListener('click', this.onModeToggle);
    }

    if (this.speedSlider) {
      this.speedSlider.removeEventListener('input', this.onSpeedChange);
    }

    this.planetButtons.forEach(button => {
      button.removeEventListener('click', this.onPlanetSelect);
    });

    if (this.helpToggle) {
      this.helpToggle.removeEventListener('click', this.onHelpToggle);
    }

    console.log('🧹 UIManager disposed');
  }
}
