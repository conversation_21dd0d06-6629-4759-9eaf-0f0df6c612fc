body {
    margin: 0;
    overflow: hidden;
    background: #000;
    color: #fff;
    font-family: Arial, sans-serif;
}

#container {
    position: absolute;
    width: 100%;
    height: 100%;
}

#ui {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
    background: rgba(0, 0, 0, 0.7);
    padding: 15px;
    border-radius: 10px;
    max-width: 200px;
}

#mode-toggle button {
    width: 100%;
    padding: 10px;
    background: #4CAF50;
    border: none;
    color: white;
    cursor: pointer;
    border-radius: 5px;
    transition: background 0.3s;
}

#mode-toggle button:hover {
    background: #45a049;
}

#speed-control {
    margin: 10px 0;
}

#speed-slider {
    width: 100%;
}

#planet-buttons button {
    display: block;
    width: 100%;
    margin: 5px 0;
    padding: 8px;
    background: #2196F3;
    border: none;
    color: white;
    cursor: pointer;
    border-radius: 5px;
    transition: background 0.3s;
}

#planet-buttons button:hover {
    background: #1E88E5;
}

#controls-hint {
    margin-top: 10px;
    font-size: 12px;
    text-align: center;
    color: #ccc;
}

/* Responsive for mobile */
@media (max-width: 600px) {
    #ui {
        top: auto;
        bottom: 0;
        left: 0;
        right: 0;
        max-width: none;
        border-radius: 10px 10px 0 0;
    }
} 